#Requires AutoHotkey v2.0
#SingleInstance Force

; --- Coordinate mode and general settings ---
CoordMode "Pixel", "Screen"
CoordMode "Mouse", "Screen"
SetDefaultMouseSpeed 0

; --- Settings container ---
class Settings {
    ; Path to the image we search for (provide kicked.png in images/)
    static ImagePath := A_ScriptDir "\\images\\kicked.png"

    ; Search region (computed from the four provided corners)
    ; TL (-1427, -1427), TR (-16, -1423), BL (-1424, -863), BR (-20, -862)
    ; Bounding rectangle => Left=-1427, Top=-1427, Right=-16, Bottom=-862
    static Region := Map("L", -1427, "T", -1427, "R", -16, "B", -862)

    ; Predetermined click point
    static Click := Map("X", -725, "Y", -1049)

    ; ImageSearch options (e.g., tolerance: "*100"; grayscale: "*Gray")
    static SearchOptions := ""  ; customize as needed, e.g., "*100"

    ; Optional delay (ms) before clicking after a detection
    static DelayBeforeClick := 50

    ; Logging
    static LogFile := A_ScriptDir "\\logs\\automation.log"
}

; --- Utilities ---
EnsureLogDir() {
    SplitPath Settings.LogFile,, &dir
    try DirCreate dir, 1
}

Log(msg) {
    t := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
    try FileAppend t "  " msg "`r`n", Settings.LogFile, "UTF-8"
    OutputDebug msg
}

FindInRegion(imagePath, region, options := "") {
    if !FileExist(imagePath)
        throw Error("Image file not found: " imagePath)

    x := 0, y := 0
    query := Trim(options) = "" ? imagePath : options " " imagePath
    found := ImageSearch(&x, &y, region["L"], region["T"], region["R"], region["B"], query)
    return Map("found", found, "x", x, "y", y)
}

Task1_RunOnce() {
    try {
        EnsureLogDir()
        res := FindInRegion(Settings.ImagePath, Settings.Region, Settings.SearchOptions)
        if res["found"] {
            Sleep Settings.DelayBeforeClick
            Click Settings.Click["X"], Settings.Click["Y"]
            msg := "Task1: Image found at (" res["x"] "," res["y"] "). Clicked at (" Settings.Click["X"] "," Settings.Click["Y"] ")."
            TrayTip "AHK Task1", msg, 1500
            Log(msg)
            return true
        } else {
            msg := "Task1: Image NOT found in region."
            TrayTip "AHK Task1", msg, 1500
            Log(msg)
            return false
        }
    } catch e {
        TrayTip "AHK Task1 - Error", e.Message, 3000
        Log("Task1 ERROR: " e.Message)
        return false
    }
}

; --- Default hotkeys (customize as desired) ---
Hotkey("F8", (*) => Task1_RunOnce()) ; Run Task 1 once
Hotkey("Esc", (*) => ExitApp())      ; Quit script

; Optional CLI usage: `AutoHotkey64.exe task1_image_click.ahk /once`
if A_Args.Length && A_Args[1] = "/once"
    Task1_RunOnce()

