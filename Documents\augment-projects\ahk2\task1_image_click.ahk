#Requires AutoHotkey v2.0
#SingleInstance Force

; --- Coordinate mode and general settings ---
CoordMode "Pixel", "Screen"
CoordMode "Mouse", "Screen"
SetDefaultMouseSpeed 0

; --- Settings container ---
class Settings {
    ; Path to the image we search for (provide kicked.png in images/)
    static ImagePath := A_ScriptDir "\\images\\kicked.png"

    ; Search region (computed from the four provided corners)
    ; TL (-1427, -1427), TR (-16, -1423), BL (-1424, -863), BR (-20, -862)
    ; Bounding rectangle => Left=-1427, Top=-1427, Right=-16, Bottom=-862
    static Region := Map("L", -1427, "T", -1427, "R", -16, "B", -862)

    ; Predetermined click point
    static Click := Map("X", -725, "Y", -1049)

    ; ImageSearch options (e.g., tolerance: "*100"; grayscale: "*Gray")
    static SearchOptions := ""  ; customize as needed, e.g., "*100"

    ; Optional delay (ms) before clicking after a detection
    static DelayBeforeClick := 50

    ; Logging
    static LogFile := A_ScriptDir "\\logs\\automation.log"
}

; --- Utilities ---
EnsureLogDir() {
    SplitPath Settings.LogFile,, &dir
    try DirCreate dir
}

Log(msg) {
    t := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
    try FileAppend t "  " msg "`r`n", Settings.LogFile, "UTF-8"
    OutputDebug msg
}

FindInRegion(imagePath, region, options := "") {
    if !FileExist(imagePath)
        throw Error("Image file not found: " imagePath)

    x := 0, y := 0
    query := Trim(options) = "" ? imagePath : options " " imagePath
    found := ImageSearch(&x, &y, region["L"], region["T"], region["R"], region["B"], query)
    return Map("found", found, "x", x, "y", y)
}

Task1_RunOnce() {
    try {
        EnsureLogDir()
        res := FindInRegion(Settings.ImagePath, Settings.Region, Settings.SearchOptions)
        if res["found"] {
            Sleep Settings.DelayBeforeClick
            Click Settings.Click["X"], Settings.Click["Y"]
            msg := "Task1: Image found at (" res["x"] "," res["y"] "). Clicked at (" Settings.Click["X"] "," Settings.Click["Y"] ")."
            TrayTip "AHK Task1", msg, 1500
            Log(msg)
            return true
        } else {
            msg := "Task1: Image NOT found in region."
            TrayTip "AHK Task1", msg, 1500
            Log(msg)
            return false
        }
    } catch e {
        TrayTip "AHK Task1 - Error", e.Message, 3000
        Log("Task1 ERROR: " e.Message)
        return false
    }
}

; --- Default hotkeys (customize as desired) ---
Hotkey("F8", (*) => Task1_RunOnce()) ; Run Task 1 once
Hotkey("Esc", (*) => ExitApp())      ; Quit script

; Optional CLI usage: `AutoHotkey64.exe task1_image_click.ahk /once`
if A_Args.Length && A_Args[1] = "/once"
    Task1_RunOnce()



; ================= GUI Controller (integrated) =================
; Provides: toggle monitoring, run-once, settings editing, feedback

; --- runtime flags ---
gActive := false
gPollInterval := 1000 ; ms (change in GUI)

; --- build GUI ---
ui := Gui(, "Image Detection Controller")

; Row 1: toggle + status + run once
ui.Add("GroupBox", "w540 h70", "Control")
activeChk := ui.Add("CheckBox", "x+10 yp+22", "Active (continuous)")
statusTxt := ui.Add("Text", "x+15 yp+2 w240", "Status: Inactive")
runOnceBtn := ui.Add("Button", "x+15 yp-4 w100", "Run Once")

; Row 2: Image path
ui.Add("GroupBox", "xm y+10 w540 h70", "Image File")
ui.Add("Text", "xm+10 yp+25", "Path:")
imgEdit := ui.Add("Edit", "x+8 w400", Settings.ImagePath)
imgBrowse := ui.Add("Button", "x+8 w90", "Browse...")

; Row 3: Region L T R B
ui.Add("GroupBox", "xm y+10 w540 h95", "Search Region (screen coords)")
ui.Add("Text", "xm+10 yp+25", "L:")
regL := ui.Add("Edit", "x+5 w70", Settings.Region["L"])
ui.Add("Text", "x+10", "T:")
regT := ui.Add("Edit", "x+5 w70", Settings.Region["T"])
ui.Add("Text", "x+10", "R:")
regR := ui.Add("Edit", "x+5 w70", Settings.Region["R"])
ui.Add("Text", "x+10", "B:")
regB := ui.Add("Edit", "x+5 w70", Settings.Region["B"])

; Row 4: Click target X Y
ui.Add("GroupBox", "xm y+10 w540 h70", "Click Target")
ui.Add("Text", "xm+10 yp+25", "X:")
clkX := ui.Add("Edit", "x+5 w90", Settings.Click["X"])
ui.Add("Text", "x+10", "Y:")
clkY := ui.Add("Edit", "x+5 w90", Settings.Click["Y"])

; Row 5: Options + interval + buttons
ui.Add("GroupBox", "xm y+10 w540 h110", "Detection Options")
ui.Add("Text", "xm+10 yp+25", "ImageSearch options:")
optEdit := ui.Add("Edit", "x+8 w380", Settings.SearchOptions)
ui.Add("Text", "xm+10 y+10", "Polling interval (ms):")
intEdit := ui.Add("Edit", "x+8 w100", gPollInterval)
applyBtn := ui.Add("Button", "x+20 yp-2 w90", "Apply")
exitBtn := ui.Add("Button", "x+10 yp-2 w80", "Exit")

; Row 6: feedback
ui.Add("GroupBox", "xm y+10 w540 h160", "Feedback")
lastTxt := ui.Add("Text", "xm+10 yp+25 w510", "Last: (none)")
feedback := ui.Add("Edit", "xm+10 y+5 w510 h110 ReadOnly -Wrap")

; --- events ---
activeChk.OnEvent("Click", ToggleActive)
runOnceBtn.OnEvent("Click", RunOnceFromGUI)
imgBrowse.OnEvent("Click", BrowseImage)
applyBtn.OnEvent("Click", ApplySettings)
exitBtn.OnEvent("Click", (*) => ExitApp())

ui.Show()
UpdateStatus(false)

; --- event handlers ---
ToggleActive(*) {
    global gActive, activeChk, gPollInterval
    gActive := !!activeChk.Value
    if gActive {
        UpdateStatus(true)
        SetTimer MonitorTick, gPollInterval
        AppendFeedback("Monitoring started (" gPollInterval " ms)")
    } else {
        SetTimer MonitorTick, 0
        UpdateStatus(false)
        AppendFeedback("Monitoring stopped")
    }
}

RunOnceFromGUI(*) {
    res := Task1_RunOnce()
    UpdateLast(res ? "Success" : "Not Found / Error")
}

BrowseImage(*) {
    file := FileSelect("3", Settings.ImagePath, "Select image", "Images (*.png; *.bmp; *.jpg; *.jpeg)")
    if file
        imgEdit.Value := file
}

ApplySettings(*) {
    try {
        Settings.ImagePath := imgEdit.Value
        l := regL.Value + 0, t := regT.Value + 0, r := regR.Value + 0, b := regB.Value + 0
        Settings.Region := Map("L", l, "T", t, "R", r, "B", b)
        Settings.Click := Map("X", clkX.Value + 0, "Y", clkY.Value + 0)
        Settings.SearchOptions := optEdit.Value
        global gPollInterval
        val := intEdit.Value + 0
        gPollInterval := val < 50 ? 50 : val
        if gActive
            SetTimer MonitorTick, gPollInterval
        AppendFeedback("Settings applied")
    } catch e {
        AppendFeedback("Apply failed: " e.Message)
    }
}

MonitorTick() {
    static busy := false
    if busy
        return
    busy := true
    try {
        res := Task1_RunOnce()
        UpdateLast(res ? "Success" : "Not Found")
    } finally {
        busy := false
    }
}

; --- helpers ---
UpdateStatus(active) {
    statusTxt.Value := active ? "Status: Active" : "Status: Inactive"
}

UpdateLast(msg) {
    t := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
    lastTxt.Value := "Last: " msg " @ " t
    AppendFeedback(msg)
}

AppendFeedback(msg) {
    t := FormatTime(A_Now, "HH:mm:ss")
    feedback.Value .= "[" t "] " msg "`r`n"
    feedback.Focus()
    Send "^{End}"
}
