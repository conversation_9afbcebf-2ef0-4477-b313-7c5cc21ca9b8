"""
Image recognition + automated click (Python)

Features
- Search a defined screen region for a target image
- If found, click a predetermined coordinate
- Logging to logs/automation.log
- CLI usage: run once, or continuous monitoring with interval

Dependencies
    pip install opencv-python numpy mss

Notes
- Works on Windows with multi-monitor setups (negative coordinates supported)
- Uses ctypes (SetCursorPos + mouse_event) for clicks to allow negative coords
- Region is a 4-tuple (left, top, right, bottom) using Windows virtual-screen coords
"""
from __future__ import annotations

import argparse
import ctypes
import logging
import os
from dataclasses import dataclass
from datetime import datetime
from typing import Tuple

import cv2  # type: ignore
import numpy as np  # type: ignore
import mss  # type: ignore

# ---------------- Settings ----------------
@dataclass
class Settings:
    # Path to image to search for
    image_path: str = os.path.join(os.path.dirname(__file__), "images", "kicked.png")

    # Search region: L, T, R, B (virtual screen coords; negatives allowed)
    region: Tuple[int, int, int, int] = (-1427, -1427, -16, -862)

    # Predetermined click location (virtual screen coords)
    click_x: int = -725
    click_y: int = -1049

    # Template matching threshold (0..1). Adjust as needed.
    threshold: float = 0.9

    # Polling interval (seconds) when watching continuously
    interval: float = 1.0

    # Optional: set True to convert images to grayscale before matching
    grayscale: bool = False

    # Logging
    log_file: str = os.path.join(os.path.dirname(__file__), "logs", "automation.log")


SETTINGS = Settings()

# ---------------- Logging ----------------
def ensure_log_dir(path: str) -> None:
    folder = os.path.dirname(path)
    if folder and not os.path.exists(folder):
        os.makedirs(folder, exist_ok=True)


def setup_logging() -> None:
    ensure_log_dir(SETTINGS.log_file)
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s %(message)s",
        handlers=[
            logging.FileHandler(SETTINGS.log_file, encoding="utf-8"),
            logging.StreamHandler(),
        ],
    )


# ---------------- Screen capture + search ----------------
def _cap_region(region: Tuple[int, int, int, int]) -> np.ndarray:
    L, T, R, B = region
    width = max(0, R - L)
    height = max(0, B - T)
    if width == 0 or height == 0:
        raise ValueError(f"Invalid region size from {region}")
    with mss.mss() as sct:
        monitor = {"left": L, "top": T, "width": width, "height": height}
        img = sct.grab(monitor)
    frame = np.array(img)
    # mss returns BGRA; convert to BGR
    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
    return frame


def _load_template(path: str) -> np.ndarray:
    if not os.path.exists(path):
        raise FileNotFoundError(f"Image file not found: {path}")
    tmpl = cv2.imread(path, cv2.IMREAD_COLOR)
    if tmpl is None:
        raise RuntimeError(f"Failed to read image: {path}")
    return tmpl


def find_in_region(image_path: str, region: Tuple[int, int, int, int], *,
                   threshold: float = 0.9, grayscale: bool = False) -> Tuple[bool, Tuple[int, int] | None, float]:
    screen = _cap_region(region)
    tmpl = _load_template(image_path)

    if grayscale:
        screen_g = cv2.cvtColor(screen, cv2.COLOR_BGR2GRAY)
        tmpl_g = cv2.cvtColor(tmpl, cv2.COLOR_BGR2GRAY)
        res = cv2.matchTemplate(screen_g, tmpl_g, cv2.TM_CCOEFF_NORMED)
    else:
        res = cv2.matchTemplate(screen, tmpl, cv2.TM_CCOEFF_NORMED)

    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
    found = max_val >= threshold
    loc = (max_loc[0], max_loc[1]) if found else None
    return found, loc, float(max_val)


# ---------------- Clicking ----------------
# Windows constants
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004

user32 = ctypes.windll.user32


def click_at(x: int, y: int, delay_ms: int = 50) -> None:
    # Set cursor position in virtual screen coordinates (supports negatives)
    user32.SetCursorPos(int(x), int(y))
    # Press and release left button
    user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
    if delay_ms > 0:
        ctypes.windll.kernel32.Sleep(int(delay_ms))
    user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)


# ---------------- Core routines ----------------
def run_once() -> bool:
    try:
        found, loc, score = find_in_region(
            SETTINGS.image_path,
            SETTINGS.region,
            threshold=SETTINGS.threshold,
            grayscale=SETTINGS.grayscale,
        )
        if found:
            click_at(SETTINGS.click_x, SETTINGS.click_y)
            logging.info(
                "FOUND (score=%.3f) at local=(%d,%d) in region LTRB=%s; clicked at (%d,%d)",
                score, loc[0], loc[1], SETTINGS.region, SETTINGS.click_x, SETTINGS.click_y,
            )
            return True
        else:
            logging.info(
                "NOT FOUND (score=%.3f) in region LTRB=%s (image=%s)",
                score, SETTINGS.region, SETTINGS.image_path,
            )
            return False
    except Exception as e:
        logging.exception("ERROR during run_once: %s", e)
        return False


def watch_loop(interval: float | None = None) -> None:
    if interval is None:
        interval = SETTINGS.interval
    try:
        while True:
            run_once()
            ctypes.windll.kernel32.Sleep(int(max(50, interval * 1000)))
    except KeyboardInterrupt:
        logging.info("Stopped by user")


# ---------------- CLI ----------------
def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Image recognition + click")
    p.add_argument("--image", default=SETTINGS.image_path, help="Path to target image")
    p.add_argument("--region", nargs=4, type=int, metavar=("L", "T", "R", "B"),
                   default=list(SETTINGS.region), help="Search region coordinates")
    p.add_argument("--click", nargs=2, type=int, metavar=("X", "Y"),
                   default=[SETTINGS.click_x, SETTINGS.click_y], help="Click coordinate")
    p.add_argument("--threshold", type=float, default=SETTINGS.threshold, help="Match threshold 0..1")
    p.add_argument("--interval", type=float, default=SETTINGS.interval, help="Polling interval in seconds")
    p.add_argument("--grayscale", action="store_true", default=SETTINGS.grayscale, help="Use grayscale matching")
    p.add_argument("--once", action="store_true", help="Run once and exit")
    return p


def main(argv: list[str] | None = None) -> int:
    setup_logging()
    parser = build_parser()
    args = parser.parse_args(argv)

    # Apply overrides
    SETTINGS.image_path = args.image
    SETTINGS.region = tuple(args.region)  # type: ignore
    SETTINGS.click_x, SETTINGS.click_y = args.click
    SETTINGS.threshold = args.threshold
    SETTINGS.interval = args.interval
    SETTINGS.grayscale = args.grayscale

    logging.info("Starting image_clicker: image=%s region=%s click=(%d,%d) th=%.2f gray=%s",
                 SETTINGS.image_path, SETTINGS.region, SETTINGS.click_x, SETTINGS.click_y,
                 SETTINGS.threshold, SETTINGS.grayscale)

    if args.once:
        ok = run_once()
        return 0 if ok else 1
    else:
        watch_loop(SETTINGS.interval)
        return 0


if __name__ == "__main__":
    raise SystemExit(main())

