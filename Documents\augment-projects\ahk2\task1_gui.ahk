#Requires AutoHotkey v2.0
#SingleInstance Force

; GUI controller for Task 1 image detection
; Supplements task1_image_click.ahk while keeping its hotkeys and logic

; Important: this file assumes it lives in the same folder as task1_image_click.ahk
#Include task1_image_click.ahk

; -------- Runtime state --------
gActive := false
gPollInterval := 1000 ; ms, can be changed from GUI

; -------- GUI --------
gui := Gui(, "Image Detection Controller")

; Row 1: Toggle + Status
row := gui.Add("GroupBox", "w520 h70", "Control")
activeChk := gui.Add("CheckBox", "x+10 yp+20 vActiveChk", "Active (continuous)")
statusTxt := gui.Add("Text", "x+20 yp+2 w260 vStatusTxt", "Status: Inactive")
runOnceBtn := gui.Add("Button", "x+20 yp-2 w90 vRunOnceBtn", "Run Once")

; Row 2: Image path
sec := gui.Add("GroupBox", "xm y+10 w520 h70", "Image File")
imgLbl := gui.Add("Text", "xm+10 yp+25", "Path:")
imgEdit := gui.Add("Edit", "x+10 w380 vImgPath", Settings.ImagePath)
imgBrowse := gui.Add("Button", "x+10 w80", "Browse...")

; Row 3: Region
regGrp := gui.Add("GroupBox", "xm y+10 w520 h100", "Search Region (Screen coords)")
regLbl1 := gui.Add("Text", "xm+10 yp+25", "L:")
regL := gui.Add("Edit", "x+5 w70 vRegL", Settings.Region["L"]) 
regLbl2 := gui.Add("Text", "x+10", "T:")
regT := gui.Add("Edit", "x+5 w70 vRegT", Settings.Region["T"]) 
regLbl3 := gui.Add("Text", "x+10", "R:")
regR := gui.Add("Edit", "x+5 w70 vRegR", Settings.Region["R"]) 
regLbl4 := gui.Add("Text", "x+10", "B:")
regB := gui.Add("Edit", "x+5 w70 vRegB", Settings.Region["B"]) 

; Row 4: Click target
clkGrp := gui.Add("GroupBox", "xm y+10 w520 h70", "Click Target")
clkLbl1 := gui.Add("Text", "xm+10 yp+25", "X:")
clkX := gui.Add("Edit", "x+5 w90 vClkX", Settings.Click["X"]) 
clkLbl2 := gui.Add("Text", "x+10", "Y:")
clkY := gui.Add("Edit", "x+5 w90 vClkY", Settings.Click["Y"]) 

; Row 5: Options
optGrp := gui.Add("GroupBox", "xm y+10 w520 h100", "Detection Options")
optLbl := gui.Add("Text", "xm+10 yp+25", "ImageSearch options:")
optEdit := gui.Add("Edit", "x+10 w370 vOptStr", Settings.SearchOptions)

intLbl := gui.Add("Text", "xm+10 y+10", "Polling interval (ms):")
intEdit := gui.Add("Edit", "x+10 w100 vPollInt", gPollInterval)

applyBtn := gui.Add("Button", "x+20 yp-2 w90", "Apply")
exitBtn := gui.Add("Button", "x+10 yp-2 w80", "Exit")

; Row 6: Feedback
fbGrp := gui.Add("GroupBox", "xm y+10 w520 h160", "Feedback")
lastTxt := gui.Add("Text", "xm+10 yp+25 w490 vLastTxt", "Last: (none)")
feedback := gui.Add("Edit", "xm+10 y+5 w490 h110 vFeedback ReadOnly -Wrap")

; Event wiring
activeChk.OnEvent("Click", ToggleActive)
runOnceBtn.OnEvent("Click", RunOnceFromGUI)
imgBrowse.OnEvent("Click", BrowseImage)
applyBtn.OnEvent("Click", ApplySettings)
exitBtn.OnEvent("Click", (*) => ExitApp())

gui.Show()
UpdateStatus(false)

; -------- Event handlers --------
ToggleActive(*) {
    global gActive, activeChk, gPollInterval
    gActive := !!activeChk.Value
    if gActive {
        UpdateStatus(true)
        SetTimer MonitorTick, gPollInterval
        AppendFeedback("Monitoring started (" gPollInterval " ms)")
    } else {
        SetTimer MonitorTick, 0
        UpdateStatus(false)
        AppendFeedback("Monitoring stopped")
    }
}

RunOnceFromGUI(*) {
    res := Task1_RunOnce()
    if res {
        UpdateLast("Success")
    } else {
        UpdateLast("Not Found / Error")
    }
}

BrowseImage(*) {
    file := FileSelect(3, Settings.ImagePath, "Select image", "Images (*.png; *.bmp; *.jpg; *.jpeg)")
    if file {
        imgEdit.Value := file
    }
}

ApplySettings(*) {
    try {
        Settings.ImagePath := imgEdit.Value
        ; parse region
        l := Integer(regL.Value), t := Integer(regT.Value), r := Integer(regR.Value), b := Integer(regB.Value)
        Settings.Region := Map("L", l, "T", t, "R", r, "B", b)
        ; click
        Settings.Click := Map("X", Integer(clkX.Value), "Y", Integer(clkY.Value))
        ; options
        Settings.SearchOptions := optEdit.Value
        ; poll interval
        global gPollInterval
        gPollInterval := Max(50, Integer(intEdit.Value))
        if gActive
            SetTimer MonitorTick, gPollInterval

        AppendFeedback("Settings applied")
    } catch e {
        AppendFeedback("Apply failed: " e.Message)
    }
}

MonitorTick() {
    static busy := false
    if busy
        return
    busy := true
    try {
        res := Task1_RunOnce()
        UpdateLast(res ? "Success" : "Not Found")
    } finally {
        busy := false
    }
}

; -------- Helpers --------
UpdateStatus(active) {
    statusTxt.Value := active ? "Status: Active" : "Status: Inactive"
}

UpdateLast(msg) {
    t := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
    lastTxt.Value := "Last: " msg " @ " t
    AppendFeedback(msg)
}

AppendFeedback(msg) {
    t := FormatTime(A_Now, "HH:mm:ss")
    feedback.Value .= "[" t "] " msg "`r`n"
    feedback.Focus() ; scroll to end
    Send "^{End}"
}

